@import 'variables';

:root {
  --menuItem-bgActiveColor: #2BABF6;
}

#app {
  .main-container {
    height: 100%;
    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
  }

  .sidebar-container {
    background: linear-gradient(180deg, #124B9A, #0D438D);
    transition: width 0.28s;
    width: $sideBarWidth !important;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
      margin-right: -8px !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }


    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
      fill: currentColor;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
      user-select: none;
      background: transparent;

      .el-menu-item {
        transition: all .5s;
        height: 61px;
        line-height: 61px;
        color: #fefefe;
        font-size: 18px;
        display: flex;
        align-items: center;
        width: 210px;
        margin:  0 auto;
        background: transparent;
        svg{
          font-size: 20px;
        }
        &:hover,
        &.is-active {
          background: url('../images/当前菜单背景.png') no-repeat;
          background-size: cover;
          svg,
          span {
            color: #17d2ef;
            font-weight: bold;
            background: linear-gradient(0deg, #17d2ef 0%, #ffffff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }

      // 子菜单特殊样式 - 放在这里确保优先级
      .nest-menu .el-menu-item,
      .nest-menu-item {
        padding-left: 60px !important;
        font-size: 16px !important;
        height: 50px !important;
        line-height: 50px !important;
        color: rgba(254, 254, 254, 0.8) !important;
        width: 190px !important;
        margin-left: 25px !important;
        position: relative;

        &:before {
          content: '';
          position: absolute;
          left: 35px;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 4px;
          background: rgba(254, 254, 254, 0.6);
          border-radius: 50%;
        }

        .svg-icon {
          font-size: 16px !important;
          margin-right: 12px !important;
          margin-left: 8px !important;
        }

        img {
          width: 13px !important;
          height: 13px !important;
          margin-right: 12px !important;
          margin-left: 8px !important;
        }

        &:hover,
        &.is-active {
          &:before {
            background: #17d2ef;
            box-shadow: 0 0 6px rgba(23, 210, 239, 0.8);
          }

          svg,
          span {
            font-weight: normal !important;
          }
        }
      }
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      height: 61px;
        line-height: 61px;
        color: #fefefe;
        font-size: 18px;
        display: flex;
        align-items: center;
        width: 210px;
        margin:  0 auto;
      position: relative;
      font-weight: bold; // 父菜单加粗
      svg{
        font-size: 20px;
      }
      i{
        font-size: 18px;
        color: #fefefe;
      }

      // 为父菜单添加左侧边框指示器
      &:before {
        content: '';
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 20px;
        background: linear-gradient(180deg, #17d2ef 0%, #ffffff 100%);
        border-radius: 2px;
        opacity: 0.8;
      }

      &:hover,&.focus{
        color: #17d2ef;
        background: transparent;

        &:before {
          opacity: 1;
          box-shadow: 0 0 8px rgba(23, 210, 239, 0.6);
        }
      }
    }
  }


  .hideSidebar {
    .sidebar-container {
      width: 60px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;
      width: 60px !important;
      background: transparent !important;
      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      &>.el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }

        // 折叠状态下隐藏父菜单指示器
        &:before {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  // 子菜单样式区分
  .nest-menu {
    .el-menu-item {
      padding-left: 60px !important; // 子菜单增加更多左侧缩进
      font-size: 16px !important; // 子菜单字体稍小
      height: 50px !important; // 子菜单高度稍小
      line-height: 50px !important;
      color: rgba(254, 254, 254, 0.8) !important; // 子菜单颜色稍淡
      width: 190px !important; // 子菜单宽度稍小
      margin-left: 25px !important; // 子菜单整体向右偏移
      position: relative;
      text-indent: 0 !important; // 重置文本缩进

      // 为子菜单添加小圆点指示器
      &:before {
        content: '';
        position: absolute;
        left: 35px;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 4px;
        background: rgba(254, 254, 254, 0.6);
        border-radius: 50%;
      }

      // 子菜单图标样式
      .svg-icon {
        font-size: 16px !important; // 子菜单图标稍小
        margin-right: 12px !important;
        margin-left: 8px !important; // 图标左边距
      }

      img {
        width: 13px !important; // 子菜单图片图标稍小
        height: 13px !important;
        margin-right: 12px !important;
        margin-left: 8px !important; // 图标左边距
      }

      &:hover,
      &.is-active {
        background: url('../images/当前菜单背景.png') no-repeat;
        background-size: cover;

        &:before {
          background: #17d2ef;
          box-shadow: 0 0 6px rgba(23, 210, 239, 0.8);
        }

        svg,
        span {
          color: #17d2ef;
          font-weight: normal !important; // 子菜单不加粗
          background: linear-gradient(0deg, #17d2ef 0%, #ffffff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }

    .el-submenu>.el-submenu__title {
      padding-left: 40px !important; // 嵌套子菜单标题缩进
      font-size: 16px !important;
      height: 50px !important;
      line-height: 50px !important;
      color: rgba(254, 254, 254, 0.8) !important;
      width: 200px !important;
      margin-left: 20px !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

// 强制子菜单样式 - 最高优先级
.sidebar-container .el-menu .nest-menu .el-menu-item,
.sidebar-container .el-menu .nest-menu-item {
  padding-left: 60px !important;
  font-size: 16px !important;
  height: 50px !important;
  line-height: 50px !important;
  color: rgba(254, 254, 254, 0.8) !important;
  width: 190px !important;
  margin-left: 25px !important;
  position: relative;

  &:before {
    content: '';
    position: absolute;
    left: 35px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    background: rgba(254, 254, 254, 0.6);
    border-radius: 50%;
  }

  .svg-icon {
    font-size: 16px !important;
    margin-right: 12px !important;
    margin-left: 8px !important;
  }

  img {
    width: 13px !important;
    height: 13px !important;
    margin-right: 12px !important;
    margin-left: 8px !important;
  }

  &:hover,
  &.is-active {
    background: url('../images/当前菜单背景.png') no-repeat;
    background-size: cover;

    &:before {
      background: #17d2ef;
      box-shadow: 0 0 6px rgba(23, 210, 239, 0.8);
    }

    svg,
    span {
      color: #17d2ef;
      font-weight: normal !important;
      background: linear-gradient(0deg, #17d2ef 0%, #ffffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
